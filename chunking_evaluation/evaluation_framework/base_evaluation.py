"""
文本分块评估框架的基础评估类模块

这个模块包含了用于评估不同文本分块策略效果的核心功能。
主要用于测量分块算法在信息检索任务中的性能表现。
"""

from typing import Callable
from chunking_evaluation.utils import rigorous_document_search, get_openai_embedding_function
import chromadb.utils.embedding_functions as embedding_functions
import os
import pandas as pd
import json
import chromadb
import numpy as np
from typing import List
from importlib import resources

def sum_of_ranges(ranges):
    """
    计算多个范围区间的总长度
    
    参数:
        ranges: 包含多个(start, end)元组的列表，每个元组表示一个范围区间
        
    返回:
        int: 所有范围区间长度的总和
        
    示例:
        sum_of_ranges([(0, 5), (10, 15)]) 返回 10 (5 + 5)
    """
    return sum(end - start for start, end in ranges)

def union_ranges(ranges):
    """
    合并重叠或相邻的范围区间
    
    这个函数接收一个范围列表，将所有重叠或相邻的范围合并成更大的范围。
    这在计算文本覆盖率时非常有用，可以避免重复计算重叠部分。
    
    参数:
        ranges: 包含多个(start, end)元组的列表，每个元组表示一个范围区间
        
    返回:
        list: 合并后的范围列表，不包含重叠区间
        
    示例:
        union_ranges([(0, 5), (3, 8), (10, 15)]) 返回 [(0, 8), (10, 15)]
    """
    # 根据起始索引对范围进行排序
    sorted_ranges = sorted(ranges, key=lambda x: x[0])
    
    # 用第一个范围初始化合并后的范围列表
    merged_ranges = [sorted_ranges[0]]
    
    # 遍历剩余的范围
    for current_start, current_end in sorted_ranges[1:]:
        last_start, last_end = merged_ranges[-1]
        
        # 检查当前范围是否与合并列表中的最后一个范围重叠或相邻
        if current_start <= last_end:
            # 合并两个范围，取更大的结束位置
            merged_ranges[-1] = (last_start, max(last_end, current_end))
        else:
            # 没有重叠，将当前范围作为新的范围添加
            merged_ranges.append((current_start, current_end))
    
    return merged_ranges

def intersect_two_ranges(range1, range2):
    """
    计算两个范围区间的交集
    
    这个函数用于找出两个范围区间的重叠部分。在评估文本分块时，
    用来计算分块与参考答案之间的重叠程度。
    
    参数:
        range1: 第一个范围，格式为(start, end)的元组
        range2: 第二个范围，格式为(start, end)的元组
        
    返回:
        tuple或None: 如果有交集返回(交集开始位置, 交集结束位置)，否则返回None
        
    示例:
        intersect_two_ranges((0, 10), (5, 15)) 返回 (5, 10)
        intersect_two_ranges((0, 5), (10, 15)) 返回 None
    """
    # 解包范围参数
    start1, end1 = range1
    start2, end2 = range2
    
    # 计算交集的开始位置（取两个开始位置的最大值）
    # 计算交集的结束位置（取两个结束位置的最小值）
    intersect_start = max(start1, start2)
    intersect_end = min(end1, end2)
    
    # 检查交集是否有效（开始位置小于等于结束位置）
    if intersect_start <= intersect_end:
        return (intersect_start, intersect_end)
    else:
        return None  # 如果没有交集则返回None
    
# 定义差集函数
def difference(ranges, target):
    """
    从一组范围中移除与目标范围重叠的部分
    
    这个函数用于计算范围的差集，即从原始范围列表中减去与目标范围重叠的部分。
    在评估中用于计算未被检索到的参考文本部分。
    
    参数:
    - ranges (list of tuples): 范围列表，每个元组格式为(a, b)，其中a <= b
    - target (tuple): 目标范围，格式为(c, d)，其中c <= d
    
    返回:
    - List of tuples: 移除与目标范围重叠部分后的范围列表
    
    示例:
        difference([(0, 10), (15, 25)], (5, 20)) 返回 [(0, 5), (20, 25)]
    """
    result = []
    target_start, target_end = target

    for start, end in ranges:
        if end < target_start or start > target_end:
            # 没有重叠，保留原范围
            result.append((start, end))
        elif start < target_start and end > target_end:
            # 目标范围是当前范围的子集，需要分割成两个范围
            result.append((start, target_start))
            result.append((target_end, end))
        elif start < target_start:
            # 在开始位置有重叠，保留不重叠的前半部分
            result.append((start, target_start))
        elif end > target_end:
            # 在结束位置有重叠，保留不重叠的后半部分
            result.append((target_end, end))
        # 否则，当前范围完全被目标范围包含，直接移除

    return result

def find_target_in_document(document, target):
    """
    在文档中查找目标文本的位置
    
    这个函数用于在文档中定位特定文本片段的起始和结束位置。
    
    参数:
        document (str): 要搜索的文档文本
        target (str): 要查找的目标文本
        
    返回:
        tuple或None: 如果找到返回(开始位置, 结束位置)，否则返回None
    """
    start_index = document.find(target)  # 查找目标文本的起始位置
    if start_index == -1:  # 如果没有找到
        return None
    end_index = start_index + len(target)  # 计算结束位置
    return start_index, end_index

class BaseEvaluation:
    """
    文本分块评估的基础类
    
    这个类提供了评估不同文本分块策略效果的核心功能。它通过比较分块结果
    与预定义的问题-答案对来测量分块算法的性能。
    
    主要功能包括：
    - 加载和管理问题数据集
    - 创建和管理向量数据库
    - 计算各种评估指标（精确率、召回率、IoU等）
    - 支持不同的嵌入函数和检索策略
    """
    
    def __init__(self, questions_csv_path: str, chroma_db_path=None, corpora_id_paths=None):
        """
        初始化基础评估类
        
        参数:
            questions_csv_path (str): 问题数据集CSV文件的路径
            chroma_db_path (str, 可选): ChromaDB数据库的存储路径，如果为None则使用内存数据库
            corpora_id_paths (dict, 可选): 语料库ID到文件路径的映射字典
        """
        # 存储语料库ID到路径的映射
        self.corpora_id_paths = corpora_id_paths

        # 存储问题数据集的CSV文件路径
        self.questions_csv_path = questions_csv_path

        # 初始化语料库列表
        self.corpus_list = []

        # 加载问题数据框
        self._load_questions_df()

        # 注释掉的代码是直接加载CSV的简单方式
        # self.questions_df = pd.read_csv(questions_csv_path)
        # self.questions_df['references'] = self.questions_df['references'].apply(json.loads)

        # 初始化ChromaDB客户端
        if chroma_db_path is not None:
            # 如果提供了路径，创建持久化客户端
            self.chroma_client = chromadb.PersistentClient(path=chroma_db_path)
        else:
            # 否则创建内存客户端
            self.chroma_client = chromadb.Client()

        # 标记是否为通用评估（用于区分不同类型的评估）
        self.is_general = False

    def _load_questions_df(self):
        """
        加载问题数据框
        
        这个私有方法负责从CSV文件加载问题数据，如果文件不存在则创建空的数据框。
        同时解析references列中的JSON数据，并提取所有唯一的语料库ID。
        """
        if os.path.exists(self.questions_csv_path):
            # 如果CSV文件存在，读取数据
            self.questions_df = pd.read_csv(self.questions_csv_path)
            # 将references列从JSON字符串转换为Python对象
            self.questions_df['references'] = self.questions_df['references'].apply(json.loads)
        else:
            # 如果文件不存在，创建空的数据框，包含必要的列
            self.questions_df = pd.DataFrame(columns=['question', 'references', 'corpus_id'])
        
        # 提取所有唯一的语料库ID，用于后续处理
        self.corpus_list = self.questions_df['corpus_id'].unique().tolist()

    def _get_chunks_and_metadata(self, splitter):
        """
        使用指定的分块器对所有语料库进行分块，并生成元数据
        
        这个方法是评估流程的核心步骤之一。它使用提供的分块器对每个语料库
        进行文本分块，并为每个分块生成包含位置信息的元数据。
        
        参数:
            splitter: 文本分块器对象，必须有split_text方法
            
        返回:
            tuple: (documents, metadatas)
                - documents: 所有分块文本的列表
                - metadatas: 对应的元数据列表，包含start_index, end_index, corpus_id
                
        注意:
            如果分块文本重复，元数据可能不准确，因为使用.find()方法查找起始位置。
            但对于超过1000字符的分块，这通常不是问题。
        """
        documents = []  # 存储所有分块文本
        metadatas = []  # 存储所有分块的元数据
        
        # 遍历每个语料库
        for corpus_id in self.corpus_list:
            corpus_path = corpus_id
            # 如果提供了路径映射，使用映射的路径
            if self.corpora_id_paths is not None:
                corpus_path = self.corpora_id_paths[corpus_id]
    
            # 检查操作系统并在Windows上使用UTF-8编码
            # 这可以防止读取包含非ASCII字符的文件时出现UnicodeDecodeError
            import platform
            if platform.system() == 'Windows':
                with open(corpus_path, 'r', encoding='utf-8') as file:
                    corpus = file.read()
            else:
                # 在其他系统上使用默认编码
                with open(corpus_path, 'r') as file:
                    corpus = file.read()
    
            # 使用分块器对当前语料库进行分块
            current_documents = splitter.split_text(corpus)
            current_metadatas = []
            
            # 为每个分块生成元数据
            for document in current_documents:
                try:
                    # 使用严格的文档搜索找到分块在原文中的位置
                    result = rigorous_document_search(corpus, document)
                    if result is None:
                        print(f"警告：无法在原文中找到分块文本")
                        print(f"分块文本（前100字符）: {document[:100]}...")
                        print(f"原文前100字符: {corpus[:100]}...")
                        # 尝试简单的字符串查找作为后备方案
                        if document in corpus:
                            start_index = corpus.find(document)
                            end_index = start_index + len(document)
                            print(f"使用简单字符串匹配找到位置: {start_index}-{end_index}")
                        else:
                            # 如果完全找不到，跳过这个分块
                            print(f"完全无法找到分块，跳过")
                            continue
                    else:
                        _, start_index, end_index = result
                except Exception as e:
                    print(f"Error in finding {document} in {corpus_id}: {e}")
                    print(f"分块文本（前100字符）: {document[:100]}...")
                    continue
                
                # 创建包含位置和语料库信息的元数据
                current_metadatas.append({
                    "start_index": start_index, 
                    "end_index": end_index, 
                    "corpus_id": corpus_id
                })
            
            # 将当前语料库的分块和元数据添加到总列表中
            documents.extend(current_documents)
            metadatas.extend(current_metadatas)
            
        return documents, metadatas

    def _full_precision_score(self, chunk_metadatas):
        """
        计算完整精确度分数（Precision Omega）
        
        这个方法计算分块策略的完整精确度分数，也称为Precision Omega。
        它评估所有分块与参考答案的重叠程度，不依赖于检索步骤。
        
        参数:
            chunk_metadatas (list): 所有分块的元数据列表，每个元数据包含位置和语料库信息
            
        返回:
            tuple: (ioc_scores, highlighted_chunks_count)
                - ioc_scores: 每个问题的IoC（Intersection over Coverage）分数列表
                - highlighted_chunks_count: 每个问题包含高亮内容的分块数量列表
        """
        ioc_scores = []  # 存储每个问题的IoC分数
        recall_scores = []  # 存储每个问题的召回率分数
        highlighted_chunks_count = []  # 存储每个问题包含高亮内容的分块数量

        # 遍历每个问题
        for index, row in self.questions_df.iterrows():
            # 解包问题和参考答案
            question = row['question']
            references = row['references']  # 参考答案列表
            corpus_id = row['corpus_id']  # 对应的语料库ID

            ioc_score = 0  # 初始化IoC分数
            numerator_sets = []  # 分子：重叠部分的范围集合
            denominator_chunks_sets = []  # 分母的一部分：包含高亮的分块范围集合
            # 初始化未使用的高亮部分（即未被任何分块覆盖的参考答案部分）
            unused_highlights = [(x['start_index'], x['end_index']) for x in references]

            highlighted_chunk_count = 0  # 包含高亮内容的分块计数

            # 遍历所有分块的元数据
            for metadata in chunk_metadatas:
                # 解包分块的起始和结束位置，以及语料库ID
                chunk_start, chunk_end, chunk_corpus_id = metadata['start_index'], metadata['end_index'], metadata['corpus_id']

                # 只处理属于当前问题对应语料库的分块
                if chunk_corpus_id != corpus_id:
                    continue
                
                contains_highlight = False  # 标记当前分块是否包含高亮内容

                # 检查当前分块与每个参考答案的重叠情况
                for ref_obj in references:
                    reference = ref_obj['content']  # 参考答案内容
                    ref_start, ref_end = int(ref_obj['start_index']), int(ref_obj['end_index'])
                    
                    # 计算分块与参考答案的交集
                    intersection = intersect_two_ranges((chunk_start, chunk_end), (ref_start, ref_end))
                    
                    if intersection is not None:
                        contains_highlight = True

                        # 从未使用的高亮中移除这个交集部分
                        unused_highlights = difference(unused_highlights, intersection)

                        # 将交集添加到分子集合中
                        numerator_sets = union_ranges([intersection] + numerator_sets)
                        
                        # 将分块添加到分母的分块集合中
                        denominator_chunks_sets = union_ranges([(chunk_start, chunk_end)] + denominator_chunks_sets)
            
                # 如果当前分块包含高亮内容，增加计数
                if contains_highlight:
                    highlighted_chunk_count += 1
                
            # 记录当前问题包含高亮内容的分块数量
            highlighted_chunks_count.append(highlighted_chunk_count)

            # 合并未使用的高亮和分块，形成最终的分母
            denominator_sets = union_ranges(denominator_chunks_sets + unused_highlights)
            
            # 如果有分子集合，计算IoC分数
            if numerator_sets:
                ioc_score = sum_of_ranges(numerator_sets) / sum_of_ranges(denominator_sets)
            
            ioc_scores.append(ioc_score)

            # 计算召回率：1 - (未覆盖的参考答案长度 / 总参考答案长度)
            recall_score = 1 - (sum_of_ranges(unused_highlights) / sum_of_ranges([(x['start_index'], x['end_index']) for x in references]))
            recall_scores.append(recall_score)

        return ioc_scores, highlighted_chunks_count

    def _scores_from_dataset_and_retrievals(self, question_metadatas, highlighted_chunks_count):
        """
        基于检索结果计算各种评估分数
        
        这个方法根据检索到的分块计算精确率、召回率和IoU分数。
        与_full_precision_score不同，这个方法只考虑检索到的前N个分块。
        
        参数:
            question_metadatas (list): 每个问题检索到的分块元数据列表
            highlighted_chunks_count (list): 每个问题要考虑的分块数量列表
            
        返回:
            tuple: (iou_scores, recall_scores, precision_scores)
                - iou_scores: IoU（Intersection over Union）分数列表
                - recall_scores: 召回率分数列表  
                - precision_scores: 精确率分数列表
        """
        iou_scores = []  # IoU分数列表
        recall_scores = []  # 召回率分数列表
        precision_scores = []  # 精确率分数列表
        
        # 同时遍历问题、分块数量和检索结果
        for (index, row), highlighted_chunk_count, metadatas in zip(self.questions_df.iterrows(), highlighted_chunks_count, question_metadatas):
            # 解包问题信息
            question = row['question']
            references = row['references']  # 参考答案
            corpus_id = row['corpus_id']

            numerator_sets = []  # 分子：重叠部分的范围集合
            denominator_chunks_sets = []  # 分母的分块部分
            # 初始化未使用的高亮（未被检索分块覆盖的参考答案部分）
            unused_highlights = [(x['start_index'], x['end_index']) for x in references]

            # 只考虑前highlighted_chunk_count个检索到的分块
            for metadata in metadatas[:highlighted_chunk_count]:
                # 解包分块位置信息
                chunk_start, chunk_end, chunk_corpus_id = metadata['start_index'], metadata['end_index'], metadata['corpus_id']

                # 只处理属于当前问题对应语料库的分块
                if chunk_corpus_id != corpus_id:
                    continue
                
                # 检查当前分块与每个参考答案的重叠
                for ref_obj in references:
                    reference = ref_obj['content']
                    ref_start, ref_end = int(ref_obj['start_index']), int(ref_obj['end_index'])
                    
                    # 计算分块与参考答案的交集
                    intersection = intersect_two_ranges((chunk_start, chunk_end), (ref_start, ref_end))
                    
                    if intersection is not None:
                        # 从未使用的高亮中移除交集部分
                        unused_highlights = difference(unused_highlights, intersection)

                        # 将交集添加到分子集合
                        numerator_sets = union_ranges([intersection] + numerator_sets)
                        
                        # 将分块添加到分母的分块集合
                        denominator_chunks_sets = union_ranges([(chunk_start, chunk_end)] + denominator_chunks_sets)
            
            # 计算分子值（重叠部分的总长度）
            if numerator_sets:
                numerator_value = sum_of_ranges(numerator_sets)
            else:
                numerator_value = 0

            # 计算各种分母
            recall_denominator = sum_of_ranges([(x['start_index'], x['end_index']) for x in references])  # 召回率分母：总参考答案长度
            precision_denominator = sum_of_ranges([(x['start_index'], x['end_index']) for x in metadatas[:highlighted_chunk_count]])  # 精确率分母：检索分块总长度
            iou_denominator = precision_denominator + sum_of_ranges(unused_highlights)  # IoU分母：检索分块长度 + 未覆盖的参考答案长度

            # 计算各种分数
            recall_score = numerator_value / recall_denominator  # 召回率 = 覆盖的参考答案长度 / 总参考答案长度
            recall_scores.append(recall_score)

            precision_score = numerator_value / precision_denominator  # 精确率 = 覆盖的参考答案长度 / 检索分块总长度
            precision_scores.append(precision_score)

            iou_score = numerator_value / iou_denominator  # IoU = 覆盖的参考答案长度 / (检索分块长度 + 未覆盖的参考答案长度)
            iou_scores.append(iou_score)

        return iou_scores, recall_scores, precision_scores

    def _chunker_to_collection(self, chunker, embedding_function, chroma_db_path:str = None, collection_name:str = None):
        """
        将分块器的结果转换为ChromaDB集合
        
        这个方法使用指定的分块器对语料库进行分块，然后将分块结果存储到
        ChromaDB集合中，用于后续的向量检索。
        
        参数:
            chunker: 文本分块器对象
            embedding_function: 嵌入函数，用于将文本转换为向量
            chroma_db_path (str, 可选): ChromaDB数据库路径，用于持久化存储
            collection_name (str, 可选): 集合名称
            
        返回:
            chromadb.Collection: 创建的ChromaDB集合对象
        """
        collection = None

        # 如果提供了数据库路径，尝试创建持久化集合
        if chroma_db_path is not None:
            try:
                chunk_client = chromadb.PersistentClient(path=chroma_db_path)
                # 创建集合，设置HNSW搜索参数以提高检索质量
                collection = chunk_client.create_collection(
                    collection_name, 
                    embedding_function=embedding_function, 
                    metadata={"hnsw:search_ef":50}
                )
                print("Created collection: ", collection_name)
            except Exception as e:
                print("Failed to create collection: ", e)
                pass
                # 如果创建失败，将使用下面的默认方式

        # 如果持久化集合创建失败，使用内存集合
        collection_name = "auto_chunk"
        if collection is None:
            try:
                # 先尝试删除同名集合（如果存在）
                self.chroma_client.delete_collection(collection_name)
            except ValueError as e:
                pass  # 集合不存在时会抛出ValueError，忽略即可
            
            # 创建新的内存集合
            collection = self.chroma_client.create_collection(
                collection_name, 
                embedding_function=embedding_function, 
                metadata={"hnsw:search_ef":50}
            )

        # 使用分块器获取文档分块和元数据
        docs, metas = self._get_chunks_and_metadata(chunker)

        # 批量添加文档到集合中，避免一次性添加过多数据导致内存问题
        BATCH_SIZE = 10  # 每批处理10个文档（API限制）
        for i in range(0, len(docs), BATCH_SIZE):
            # 获取当前批次的文档和元数据
            batch_docs = docs[i:i+BATCH_SIZE]
            batch_metas = metas[i:i+BATCH_SIZE]
            # 为每个文档生成唯一ID
            batch_ids = [str(i) for i in range(i, i+len(batch_docs))]
            
            # 将批次数据添加到集合
            collection.add(
                documents=batch_docs,  # 文档文本
                metadatas=batch_metas,  # 元数据（包含位置信息）
                ids=batch_ids  # 文档ID
            )

            # 调试用的打印语句（已注释）
            # print("Documents: ", batch_docs)
            # print("Metadatas: ", batch_metas)

        return collection
    
    def _convert_question_references_to_json(self):
        """
        将问题数据框中的references列从JSON字符串转换为Python对象
        
        这个私有方法用于安全地解析references列中的JSON数据。
        如果解析失败，会静默跳过而不抛出异常。
        """
        def safe_json_loads(row):
            """
            安全的JSON解析函数
            
            参数:
                row: 要解析的JSON字符串
                
            返回:
                解析后的Python对象，如果解析失败则返回None
            """
            try:
                return json.loads(row)
            except:
                pass  # 解析失败时静默跳过

        # 对references列应用安全的JSON解析
        self.questions_df['references'] = self.questions_df['references'].apply(safe_json_loads)


    def run(self, chunker, embedding_function=None, retrieve:int = 5, db_to_save_chunks: str = None, collect_debug_info: bool = False):
        """
        运行分块器评估的主要方法

        这是整个评估框架的核心方法。它使用提供的分块器对语料库进行分块，
        然后通过向量检索和多种指标来评估分块策略的效果。

        参数:
            chunker: 要评估的文本分块器对象，必须有split_text方法
            embedding_function: 用于计算向量相似度的嵌入函数。如果未提供，将使用默认的OpenAI嵌入函数
            retrieve (int): 每个问题要检索的分块数量。如果设置为-1，将检索包含摘录的最少分块数量（通常为1-3个）。
                           如果设置为具体数值，所有查询都将使用这个固定数量
            db_to_save_chunks (str, 可选): 保存分块的数据库路径，用于缓存和重用分块结果
            collect_debug_info (bool): 是否收集详细的调试信息，包括每个问题的检索结果和计算过程

        返回:
            dict: 包含各种评估指标的字典，包括：
                - corpora_scores: 每个语料库的详细分数
                - iou_mean/std: IoU分数的均值和标准差
                - recall_mean/std: 召回率的均值和标准差
                - precision_omega_mean/std: 完整精确度的均值和标准差
                - precision_mean/std: 精确率的均值和标准差
                - question_details: (如果collect_debug_info=True) 每个问题的详细调试信息
        """
        # 重新加载问题数据框，确保使用最新数据
        self._load_questions_df()
        
        # 如果没有提供嵌入函数，使用默认的OpenAI嵌入函数
        if embedding_function is None:
            embedding_function = get_openai_embedding_function()

        collection = None  # 初始化集合变量
        
        # 如果提供了数据库路径，尝试重用已存在的分块集合
        if db_to_save_chunks is not None:
            # 提取分块器的参数用于生成唯一的集合名称
            chunk_size = chunker._chunk_size if hasattr(chunker, '_chunk_size') else "0"
            chunk_overlap = chunker._chunk_overlap if hasattr(chunker, '_chunk_overlap') else "0"
            embedding_function_name = embedding_function.__class__.__name__
            
            # 简化SentenceTransformer的名称以避免名称过长
            if embedding_function_name == "SentenceTransformerEmbeddingFunction":
                embedding_function_name = "SentEmbFunc"
            
            # 生成包含所有关键参数的集合名称，确保唯一性
            collection_name = embedding_function_name + '_' + chunker.__class__.__name__ + '_' + str(int(chunk_size)) + '_' + str(int(chunk_overlap))
            
            try:
                # 尝试获取已存在的集合，避免重复计算
                chunk_client = chromadb.PersistentClient(path=db_to_save_chunks)
                collection = chunk_client.get_collection(collection_name, embedding_function=embedding_function)
            except Exception as e:
                # 如果集合不存在，get_collection会抛出异常，此时需要创建新集合
                collection = self._chunker_to_collection(chunker, embedding_function, chroma_db_path=db_to_save_chunks, collection_name=collection_name)

        # 如果没有成功获取或创建持久化集合，创建临时内存集合
        if collection is None:
            collection = self._chunker_to_collection(chunker, embedding_function)

        question_collection = None  # 初始化问题集合变量

        # 如果这是通用评估，尝试使用预计算的问题嵌入向量
        if self.is_general:
            with resources.as_file(resources.files('chunking_evaluation.evaluation_framework') / 'general_evaluation_data') as general_benchmark_path:
                # 连接到预存储问题嵌入的数据库
                questions_client = chromadb.PersistentClient(path=os.path.join(general_benchmark_path, 'questions_db'))
                
                # 根据嵌入函数类型尝试加载对应的预计算嵌入
                if embedding_function.__class__.__name__ == "OpenAIEmbeddingFunction":
                    try:
                        # 根据OpenAI模型类型选择对应的预计算嵌入集合
                        if embedding_function._model_name == "text-embedding-3-large":
                            question_collection = questions_client.get_collection("auto_questions_openai_large", embedding_function=embedding_function)
                        elif embedding_function._model_name == "text-embedding-3-small":
                            question_collection = questions_client.get_collection("auto_questions_openai_small", embedding_function=embedding_function)
                    except Exception as e:
                        print("警告：无法使用论文中原始使用的冻结嵌入向量。因此，此包现在将生成新的嵌入向量集。变化应该是最小的，只来自OpenAI嵌入函数的噪声底线。错误：", e)
                
                elif embedding_function.__class__.__name__ == "SentenceTransformerEmbeddingFunction":
                    try:
                        # 尝试加载SentenceTransformer的预计算嵌入
                        question_collection = questions_client.get_collection("auto_questions_sentence_transformer", embedding_function=embedding_function)
                    except:
                        print("警告：无法使用论文中原始使用的冻结嵌入向量。因此，此包现在将生成新的嵌入向量集。变化应该是最小的，只来自SentenceTransformer嵌入函数的噪声底线。错误：", e)
        
        # 如果不是通用评估或无法加载预计算的问题嵌入，创建新的问题集合
        if not self.is_general or question_collection is None:
            # 调试用注释：如果是通用评估但加载失败
            # if self.is_general:
            #     print("FAILED TO LOAD GENERAL EVALUATION")

            try:
                # 删除可能存在的同名集合
                self.chroma_client.delete_collection("auto_questions")
            except ValueError as e:
                pass  # 集合不存在时忽略错误
            except Exception as e:
                print(f"警告：删除auto_questions集合时出错: {e}")

            try:
                # 创建新的问题集合，用于存储问题的嵌入向量
                question_collection = self.chroma_client.create_collection(
                    "auto_questions",
                    embedding_function=embedding_function,
                    metadata={"hnsw:search_ef":50}
                )

                # 批量添加问题到集合中，避免超过API限制
                questions_list = self.questions_df['question'].tolist()
                corpus_ids_list = self.questions_df['corpus_id'].tolist()
                question_ids_list = [str(i) for i in self.questions_df.index]

                QUESTION_BATCH_SIZE = 10  # 每批处理10个问题（API限制）
                for i in range(0, len(questions_list), QUESTION_BATCH_SIZE):
                    batch_questions = questions_list[i:i+QUESTION_BATCH_SIZE]
                    batch_metadatas = [{"corpus_id": x} for x in corpus_ids_list[i:i+QUESTION_BATCH_SIZE]]
                    batch_ids = question_ids_list[i:i+QUESTION_BATCH_SIZE]

                    question_collection.add(
                        documents=batch_questions,
                        metadatas=batch_metadatas,
                        ids=batch_ids
                    )
            except Exception as e:
                print(f"错误：创建或填充问题集合失败: {e}")
                raise
        
        # 获取问题集合中的所有嵌入向量
        question_db = question_collection.get(include=['embeddings'])

        # 将ID转换为整数以便排序
        question_db['ids'] = [int(id) for id in question_db['ids']]
        # 根据ID对嵌入向量进行排序，确保与问题数据框的顺序一致
        _, sorted_embeddings = zip(*sorted(zip(question_db['ids'], question_db['embeddings'])))

        # 将问题数据框按索引升序排序，确保与嵌入向量顺序匹配
        self.questions_df = self.questions_df.sort_index()

        # 计算完整精确度分数（不依赖检索的基准分数）
        brute_iou_scores, highlighted_chunks_count = self._full_precision_score(collection.get()['metadatas'])

        # 确定检索的分块数量
        if retrieve == -1:
            # 如果设置为-1，使用自适应检索数量（最多20个，基于包含高亮的分块数量）
            maximum_n = min(20, max(highlighted_chunks_count))
        else:
            # 如果设置了固定数量，所有问题都使用相同的检索数量
            highlighted_chunks_count = [retrieve] * len(highlighted_chunks_count)
            maximum_n = retrieve

        # 调试用代码：计算嵌入向量的哈希值（已注释）
        # arr_bytes = np.array(list(sorted_embeddings)).tobytes()
        # print("Hash: ", hashlib.md5(arr_bytes).hexdigest())

        # 基于排序后的嵌入向量进行文档检索
        # 这是评估的核心步骤：使用问题的嵌入向量在分块集合中检索最相似的分块
        retrievals = collection.query(query_embeddings=list(sorted_embeddings), n_results=maximum_n)

        # 基于检索结果计算各种评估分数
        iou_scores, recall_scores, precision_scores = self._scores_from_dataset_and_retrievals(retrievals['metadatas'], highlighted_chunks_count)

        # 收集详细调试信息（如果需要）
        question_details = []
        if collect_debug_info:
            question_details = self._collect_debug_info(
                retrievals, highlighted_chunks_count,
                iou_scores, recall_scores, precision_scores, brute_iou_scores
            )

        # 按语料库组织分数，用于分析不同语料库上的性能差异
        corpora_scores = {}
        
        # 遍历每个问题，将分数按语料库分组
        for index, row in self.questions_df.iterrows():
            # 如果这个语料库还没有记录，初始化其分数字典
            if row['corpus_id'] not in corpora_scores:
                corpora_scores[row['corpus_id']] = {
                    "precision_omega_scores": [],  # 完整精确度分数列表
                    "iou_scores": [],              # IoU分数列表
                    "recall_scores": [],           # 召回率分数列表
                    "precision_scores": []         # 精确率分数列表
                }
            
            # 将当前问题的各项分数添加到对应语料库的分数列表中
            corpora_scores[row['corpus_id']]['precision_omega_scores'].append(brute_iou_scores[index])
            corpora_scores[row['corpus_id']]['iou_scores'].append(iou_scores[index])
            corpora_scores[row['corpus_id']]['recall_scores'].append(recall_scores[index])
            corpora_scores[row['corpus_id']]['precision_scores'].append(precision_scores[index])


        # 计算各项指标的总体统计信息（均值和标准差）
        
        # 完整精确度（Precision Omega）的统计信息
        brute_iou_mean = np.mean(brute_iou_scores)  # 完整精确度均值
        brute_iou_std = np.std(brute_iou_scores)   # 完整精确度标准差

        # 召回率的统计信息
        recall_mean = np.mean(recall_scores)  # 召回率均值
        recall_std = np.std(recall_scores)    # 召回率标准差

        # IoU分数的统计信息
        iou_mean = np.mean(iou_scores)  # IoU均值
        iou_std = np.std(iou_scores)    # IoU标准差

        # 精确率的统计信息
        precision_mean = np.mean(precision_scores)  # 精确率均值
        precision_std = np.std(precision_scores)    # 精确率标准差

        # 调试用的打印语句（已注释）
        # print("Recall scores: ", recall_scores)
        # print("Precision scores: ", precision_scores)
        # print("Recall Mean: ", recall_mean)
        # print("Precision Mean: ", precision_mean)

        # 构建返回结果字典
        result = {
            "corpora_scores": corpora_scores,           # 按语料库分组的详细分数
            "iou_mean": iou_mean,                       # IoU均值
            "iou_std": iou_std,                         # IoU标准差
            "recall_mean": recall_mean,                 # 召回率均值
            "recall_std": recall_std,                   # 召回率标准差
            "precision_omega_mean": brute_iou_mean,     # 完整精确度均值
            "precision_omega_std": brute_iou_std,       # 完整精确度标准差
            "precision_mean": precision_mean,           # 精确率均值
            "precision_std": precision_std              # 精确率标准差
        }

        # 如果收集了调试信息，添加到结果中
        if collect_debug_info:
            result["question_details"] = question_details

        return result

    def _collect_debug_info(self, retrievals, highlighted_chunks_count, iou_scores, recall_scores, precision_scores, brute_iou_scores):
        """
        收集每个问题的详细调试信息

        参数:
            retrievals: ChromaDB检索结果
            highlighted_chunks_count: 每个问题的检索分块数量
            iou_scores: IoU分数列表
            recall_scores: 召回率分数列表
            precision_scores: 精确率分数列表
            brute_iou_scores: 完整精确度分数列表

        返回:
            list: 每个问题的详细调试信息列表
        """
        question_details = []

        # 遍历每个问题
        for index, row in self.questions_df.iterrows():
            question_text = row['question']
            references = row['references']
            corpus_id = row['corpus_id']

            # 获取该问题的检索结果
            question_retrievals = {
                'documents': retrievals['documents'][index] if 'documents' in retrievals else [],
                'metadatas': retrievals['metadatas'][index] if 'metadatas' in retrievals else [],
                'distances': retrievals['distances'][index] if 'distances' in retrievals else []
            }

            # 构建检索到的文档块信息
            retrieved_chunks = []
            chunk_count = highlighted_chunks_count[index] if index < len(highlighted_chunks_count) else len(question_retrievals['documents'])

            for i in range(min(chunk_count, len(question_retrievals['documents']))):
                chunk_info = {
                    "rank": i + 1,
                    "content": question_retrievals['documents'][i] if i < len(question_retrievals['documents']) else "",
                    "similarity_score": 1 - question_retrievals['distances'][i] if i < len(question_retrievals['distances']) else 0.0,  # 转换距离为相似度
                    "metadata": question_retrievals['metadatas'][i] if i < len(question_retrievals['metadatas']) else {}
                }
                retrieved_chunks.append(chunk_info)

            # 构建标准答案信息
            ground_truth = []
            for ref in references:
                ground_truth.append({
                    "content": ref.get('content', ''),
                    "start_index": ref.get('start_index', 0),
                    "end_index": ref.get('end_index', 0),
                    "corpus_id": corpus_id
                })

            # 构建评估指标详情
            metrics = {
                "precision": precision_scores[index] if index < len(precision_scores) else 0.0,
                "recall": recall_scores[index] if index < len(recall_scores) else 0.0,
                "iou": iou_scores[index] if index < len(iou_scores) else 0.0,
                "precision_omega": brute_iou_scores[index] if index < len(brute_iou_scores) else 0.0
            }

            # 构建问题详情
            question_detail = {
                "question_id": index,
                "question_text": question_text,
                "corpus_id": corpus_id,
                "ground_truth": ground_truth,
                "retrieved_chunks": retrieved_chunks,
                "metrics": metrics
            }

            question_details.append(question_detail)

        return question_details
