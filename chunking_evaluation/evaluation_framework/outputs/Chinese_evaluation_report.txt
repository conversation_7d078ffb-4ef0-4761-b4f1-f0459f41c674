中文数据集分块评估报告 - 多参数配置性能分析
================================================================================

📊 成功评估的配置:
--------------------------------------------------

🔍 FixedTokenChunker:
  配置: FixedTokenChunker_config_1
  参数: chunk_size=200, chunk_overlap=20
  精确率: 0.098
  召回率: 0.978
  IoU分数: 0.098
  ----------------------------------------
  配置: FixedTokenChunker_config_2
  参数: chunk_size=200, chunk_overlap=50
  精确率: 0.098
  召回率: 0.978
  IoU分数: 0.098
  ----------------------------------------
  配置: FixedTokenChunker_config_3
  参数: chunk_size=500, chunk_overlap=50
  精确率: 0.032
  召回率: 0.933
  IoU分数: 0.032
  ----------------------------------------
  配置: FixedTokenChunker_config_4
  参数: chunk_size=500, chunk_overlap=100
  精确率: 0.032
  召回率: 0.933
  IoU分数: 0.032
  ----------------------------------------
  配置: FixedTokenChunker_config_5
  参数: chunk_size=1000, chunk_overlap=100
  精确率: 0.020
  召回率: 0.999
  IoU分数: 0.020
  ----------------------------------------
  配置: FixedTokenChunker_config_6
  参数: chunk_size=1000, chunk_overlap=200
  精确率: 0.020
  召回率: 0.999
  IoU分数: 0.020
  ----------------------------------------
  🏆 最佳IoU配置: FixedTokenChunker_config_1 (IoU: 0.098)
     参数: {'chunk_size': 200, 'chunk_overlap': 20}


🔍 RecursiveTokenChunker:
  配置: RecursiveTokenChunker_config_1
  参数: chunk_size=200, chunk_overlap=20
  精确率: 0.097
  召回率: 0.978
  IoU分数: 0.097
  ----------------------------------------
  配置: RecursiveTokenChunker_config_2
  参数: chunk_size=200, chunk_overlap=50
  精确率: 0.097
  召回率: 1.000
  IoU分数: 0.097
  ----------------------------------------
  配置: RecursiveTokenChunker_config_3
  参数: chunk_size=500, chunk_overlap=50
  精确率: 0.035
  召回率: 1.000
  IoU分数: 0.035
  ----------------------------------------
  配置: RecursiveTokenChunker_config_4
  参数: chunk_size=500, chunk_overlap=100
  精确率: 0.037
  召回率: 1.000
  IoU分数: 0.037
  ----------------------------------------
  配置: RecursiveTokenChunker_config_5
  参数: chunk_size=1000, chunk_overlap=100
  精确率: 0.018
  召回率: 1.000
  IoU分数: 0.018
  ----------------------------------------
  配置: RecursiveTokenChunker_config_6
  参数: chunk_size=1000, chunk_overlap=200
  精确率: 0.019
  召回率: 1.000
  IoU分数: 0.019
  ----------------------------------------
  🏆 最佳IoU配置: RecursiveTokenChunker_config_2 (IoU: 0.097)
     参数: {'chunk_size': 200, 'chunk_overlap': 50}


❌ 失败的配置:
--------------------------------------------------
配置: ClusterSemanticChunker_config_1
类型: ClusterSemanticChunker
参数: max_chunk_size=200, min_chunk_size=50
错误: Error code: 400 - {'error': {'code': 'InvalidParameter', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Value error, batch size is invalid, it should not be larger than 10.: input.contents', 'type': 'InvalidParameter'}, 'id': 'd3b718f0-c32b-9a96-b7ba-e75a74f44217', 'request_id': 'd3b718f0-c32b-9a96-b7ba-e75a74f44217'}
------------------------------
配置: ClusterSemanticChunker_config_2
类型: ClusterSemanticChunker
参数: max_chunk_size=400, min_chunk_size=50
错误: Error code: 400 - {'error': {'code': 'InvalidParameter', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Value error, batch size is invalid, it should not be larger than 10.: input.contents', 'type': 'InvalidParameter'}, 'id': 'bf406833-a80c-949b-aa4b-d5c30baf2fb4', 'request_id': 'bf406833-a80c-949b-aa4b-d5c30baf2fb4'}
------------------------------
配置: ClusterSemanticChunker_config_3
类型: ClusterSemanticChunker
参数: max_chunk_size=400, min_chunk_size=100
错误: division by zero
------------------------------
配置: ClusterSemanticChunker_config_4
类型: ClusterSemanticChunker
参数: max_chunk_size=600, min_chunk_size=100
错误: Error code: 400 - {'error': {'code': 'InvalidParameter', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Value error, batch size is invalid, it should not be larger than 10.: input.contents', 'type': 'InvalidParameter'}, 'id': 'd29c92be-7d8d-9af1-a402-86fd737ab431', 'request_id': 'd29c92be-7d8d-9af1-a402-86fd737ab431'}
------------------------------
配置: ClusterSemanticChunker_config_5
类型: ClusterSemanticChunker
参数: max_chunk_size=800, min_chunk_size=100
错误: Error code: 400 - {'error': {'code': 'InvalidParameter', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Value error, batch size is invalid, it should not be larger than 10.: input.contents', 'type': 'InvalidParameter'}, 'id': '964d7658-f8de-9e20-a028-c3532eaa2762', 'request_id': '964d7658-f8de-9e20-a028-c3532eaa2762'}
------------------------------
配置: ClusterSemanticChunker_config_6
类型: ClusterSemanticChunker
参数: max_chunk_size=1000, min_chunk_size=200
错误: Error code: 400 - {'error': {'code': 'InvalidParameter', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Value error, batch size is invalid, it should not be larger than 10.: input.contents', 'type': 'InvalidParameter'}, 'id': 'f281944a-9bf7-9bcb-8035-ac4887649a04', 'request_id': 'f281944a-9bf7-9bcb-8035-ac4887649a04'}
------------------------------
配置: KamradtModifiedChunker_config_1
类型: KamradtModifiedChunker
参数: avg_chunk_size=200, min_chunk_size=50
错误: Error code: 400 - {'error': {'code': 'InvalidParameter', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Value error, batch size is invalid, it should not be larger than 10.: input.contents', 'type': 'InvalidParameter'}, 'id': 'ce7a2e72-ba28-9bb9-bc9c-33f77301350a', 'request_id': 'ce7a2e72-ba28-9bb9-bc9c-33f77301350a'}
------------------------------
配置: KamradtModifiedChunker_config_2
类型: KamradtModifiedChunker
参数: avg_chunk_size=400, min_chunk_size=50
错误: division by zero
------------------------------
配置: KamradtModifiedChunker_config_3
类型: KamradtModifiedChunker
参数: avg_chunk_size=400, min_chunk_size=100
错误: division by zero
------------------------------
配置: KamradtModifiedChunker_config_4
类型: KamradtModifiedChunker
参数: avg_chunk_size=600, min_chunk_size=100
错误: division by zero
------------------------------
配置: KamradtModifiedChunker_config_5
类型: KamradtModifiedChunker
参数: avg_chunk_size=800, min_chunk_size=100
错误: division by zero
------------------------------
配置: KamradtModifiedChunker_config_6
类型: KamradtModifiedChunker
参数: avg_chunk_size=1000, min_chunk_size=200
错误: division by zero
------------------------------

🏆 全局最佳表现:
--------------------------------------------------
最佳IoU分数: FixedTokenChunker_config_1 (0.098)
  参数: {'chunk_size': 200, 'chunk_overlap': 20}
最佳精确率: FixedTokenChunker_config_1 (0.098)
  参数: {'chunk_size': 200, 'chunk_overlap': 20}
最佳召回率: RecursiveTokenChunker_config_2 (1.000)
  参数: {'chunk_size': 200, 'chunk_overlap': 50}
