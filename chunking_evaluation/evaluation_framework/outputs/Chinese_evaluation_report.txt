中文数据集分块评估报告 - 多参数配置性能分析
================================================================================


❌ 失败的配置:
--------------------------------------------------
配置: FixedTokenChunker_chunk_overlap20_chunk_size200
类型: FixedTokenChunker
参数: chunk_size=200, chunk_overlap=20
错误: Collection [auto_chunk] does not exists
------------------------------
配置: FixedTokenChunker_chunk_overlap50_chunk_size200
类型: FixedTokenChunker
参数: chunk_size=200, chunk_overlap=50
错误: Collection [auto_chunk] does not exists
------------------------------
配置: FixedTokenChunker_chunk_overlap50_chunk_size500
类型: FixedTokenChunker
参数: chunk_size=500, chunk_overlap=50
错误: Collection [auto_chunk] does not exists
------------------------------
配置: FixedTokenChunker_chunk_overlap100_chunk_size500
类型: FixedTokenChunker
参数: chunk_size=500, chunk_overlap=100
错误: Collection [auto_chunk] does not exists
------------------------------
配置: FixedTokenChunker_chunk_overlap100_chunk_size1000
类型: FixedTokenChunker
参数: chunk_size=1000, chunk_overlap=100
错误: Collection [auto_chunk] does not exists
------------------------------
配置: FixedTokenChunker_chunk_overlap200_chunk_size1000
类型: FixedTokenChunker
参数: chunk_size=1000, chunk_overlap=200
错误: Collection [auto_chunk] does not exists
------------------------------
配置: RecursiveTokenChunker_chunk_overlap20_chunk_size200
类型: RecursiveTokenChunker
参数: chunk_size=200, chunk_overlap=20
错误: Collection [auto_chunk] does not exists
------------------------------
配置: RecursiveTokenChunker_chunk_overlap50_chunk_size200
类型: RecursiveTokenChunker
参数: chunk_size=200, chunk_overlap=50
错误: Collection [auto_chunk] does not exists
------------------------------
配置: RecursiveTokenChunker_chunk_overlap50_chunk_size500
类型: RecursiveTokenChunker
参数: chunk_size=500, chunk_overlap=50
错误: Collection [auto_chunk] does not exists
------------------------------
配置: RecursiveTokenChunker_chunk_overlap100_chunk_size500
类型: RecursiveTokenChunker
参数: chunk_size=500, chunk_overlap=100
错误: Collection [auto_chunk] does not exists
------------------------------
配置: RecursiveTokenChunker_chunk_overlap100_chunk_size1000
类型: RecursiveTokenChunker
参数: chunk_size=1000, chunk_overlap=100
错误: Collection [auto_chunk] does not exists
------------------------------
配置: RecursiveTokenChunker_chunk_overlap200_chunk_size1000
类型: RecursiveTokenChunker
参数: chunk_size=1000, chunk_overlap=200
错误: Collection [auto_chunk] does not exists
------------------------------
