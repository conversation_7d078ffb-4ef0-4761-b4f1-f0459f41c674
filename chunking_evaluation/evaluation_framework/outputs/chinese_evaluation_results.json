{"FixedTokenChunker_chunk_overlap20_chunk_size200": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "FixedTokenChunker", "params": {"chunk_size": 200, "chunk_overlap": 20}, "db_name": "FixedTokenChunker_chunk_overlap20_chunk_size200", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/FixedTokenChunker_chunk_overlap20_chunk_size200", "db_reused": true}, "FixedTokenChunker_chunk_overlap50_chunk_size200": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "FixedTokenChunker", "params": {"chunk_size": 200, "chunk_overlap": 50}, "db_name": "FixedTokenChunker_chunk_overlap50_chunk_size200", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/FixedTokenChunker_chunk_overlap50_chunk_size200", "db_reused": true}, "FixedTokenChunker_chunk_overlap50_chunk_size500": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "FixedTokenChunker", "params": {"chunk_size": 500, "chunk_overlap": 50}, "db_name": "FixedTokenChunker_chunk_overlap50_chunk_size500", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/FixedTokenChunker_chunk_overlap50_chunk_size500", "db_reused": true}, "FixedTokenChunker_chunk_overlap100_chunk_size500": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "FixedTokenChunker", "params": {"chunk_size": 500, "chunk_overlap": 100}, "db_name": "FixedTokenChunker_chunk_overlap100_chunk_size500", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/FixedTokenChunker_chunk_overlap100_chunk_size500", "db_reused": true}, "FixedTokenChunker_chunk_overlap100_chunk_size1000": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "FixedTokenChunker", "params": {"chunk_size": 1000, "chunk_overlap": 100}, "db_name": "FixedTokenChunker_chunk_overlap100_chunk_size1000", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/FixedTokenChunker_chunk_overlap100_chunk_size1000", "db_reused": true}, "FixedTokenChunker_chunk_overlap200_chunk_size1000": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "FixedTokenChunker", "params": {"chunk_size": 1000, "chunk_overlap": 200}, "db_name": "FixedTokenChunker_chunk_overlap200_chunk_size1000", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/FixedTokenChunker_chunk_overlap200_chunk_size1000", "db_reused": true}, "RecursiveTokenChunker_chunk_overlap20_chunk_size200": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "RecursiveTokenChunker", "params": {"chunk_size": 200, "chunk_overlap": 20}, "db_name": "RecursiveTokenChunker_chunk_overlap20_chunk_size200", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/RecursiveTokenChunker_chunk_overlap20_chunk_size200", "db_reused": true}, "RecursiveTokenChunker_chunk_overlap50_chunk_size200": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "RecursiveTokenChunker", "params": {"chunk_size": 200, "chunk_overlap": 50}, "db_name": "RecursiveTokenChunker_chunk_overlap50_chunk_size200", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/RecursiveTokenChunker_chunk_overlap50_chunk_size200", "db_reused": true}, "RecursiveTokenChunker_chunk_overlap50_chunk_size500": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "RecursiveTokenChunker", "params": {"chunk_size": 500, "chunk_overlap": 50}, "db_name": "RecursiveTokenChunker_chunk_overlap50_chunk_size500", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/RecursiveTokenChunker_chunk_overlap50_chunk_size500", "db_reused": true}, "RecursiveTokenChunker_chunk_overlap100_chunk_size500": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "RecursiveTokenChunker", "params": {"chunk_size": 500, "chunk_overlap": 100}, "db_name": "RecursiveTokenChunker_chunk_overlap100_chunk_size500", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/RecursiveTokenChunker_chunk_overlap100_chunk_size500", "db_reused": true}, "RecursiveTokenChunker_chunk_overlap100_chunk_size1000": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "RecursiveTokenChunker", "params": {"chunk_size": 1000, "chunk_overlap": 100}, "db_name": "RecursiveTokenChunker_chunk_overlap100_chunk_size1000", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/RecursiveTokenChunker_chunk_overlap100_chunk_size1000", "db_reused": true}, "RecursiveTokenChunker_chunk_overlap200_chunk_size1000": {"error": "Collection [auto_chunk] does not exists", "chunker_type": "RecursiveTokenChunker", "params": {"chunk_size": 1000, "chunk_overlap": 200}, "db_name": "RecursiveTokenChunker_chunk_overlap200_chunk_size1000", "db_path": "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db/RecursiveTokenChunker_chunk_overlap200_chunk_size1000", "db_reused": true}}