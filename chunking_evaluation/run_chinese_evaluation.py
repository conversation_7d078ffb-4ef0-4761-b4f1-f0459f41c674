#!/usr/bin/env python3
"""
中文数据集分块评估脚本

这个脚本专门用于评估不同分块策略在中文数据集上的表现。
它使用您已创建的中文问题-答案对来测试各种分块算法的效果。

主要功能：
1. 测试多种分块策略（固定token、递归token、语义聚类等）
2. 支持多种参数配置的系统性评估
3. 使用中文数据集进行评估
4. 生成详细的评估报告和比较结果

使用方法：
python run_chinese_evaluation.py
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List, Tuple
from itertools import product

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.evaluation_framework.base_evaluation import BaseEvaluation
from chunking_evaluation.chunking import (
    FixedTokenChunker,
    RecursiveTokenChunker,
    # ClusterSemanticChunker,  # 暂时注释掉
    # KamradtModifiedChunker   # 暂时注释掉
)
from chunking_evaluation.utils import get_openai_embedding_function

class ChineseEvaluation(BaseEvaluation):
    """
    中文数据集评估类

    专门用于评估分块策略在中文数据集上的表现。
    继承自BaseEvaluation，使用中文问题-答案对进行评估。
    """

    def __init__(self, questions_csv_path: str, chroma_db_path: str = None):
        """
        初始化中文评估类

        参数:
            questions_csv_path (str): 中文问题数据集CSV文件路径
            chroma_db_path (str, 可选): ChromaDB数据库路径
        """
        # 构建语料库路径映射
        corpora_id_paths = self._build_corpora_paths()

        # 调用父类构造函数
        super().__init__(questions_csv_path, chroma_db_path=chroma_db_path, corpora_id_paths=corpora_id_paths)

    def _build_corpora_paths(self) -> Dict[str, str]:
        """
        构建语料库ID到文件路径的映射

        返回:
            Dict[str, str]: 语料库ID到文件路径的映射字典
        """
        corpora_paths = {}

        # 获取当前脚本所在目录
        current_dir = Path(__file__).parent

        # datasets目录路径
        datasets_dir = current_dir / "evaluation_framework" / "general_evaluation_data" / "datasets"

        # 检查datasets目录是否存在
        if not datasets_dir.exists():
            print(f"警告：datasets目录不存在: {datasets_dir}")
            return corpora_paths

        # 遍历datasets目录下的所有子目录
        for subdir in datasets_dir.iterdir():
            if subdir.is_dir():
                # 遍历该子目录下的所有.txt文件
                for file in subdir.iterdir():
                    if file.suffix == '.txt':
                        # 使用绝对路径作为corpus_id，以匹配CSV文件中的路径
                        absolute_path = str(file.absolute())
                        corpora_paths[absolute_path] = str(file)

        return corpora_paths

def get_chunker_configs() -> Dict[str, List[Dict[str, Any]]]:
    """
    获取所有分块器的参数配置列表

    返回:
        Dict[str, List[Dict[str, Any]]]: 分块器名称到参数配置列表的映射
    """
    configs = {}

    # 1. 固定token分块器的参数配置
    configs['FixedTokenChunker'] = [
        {'chunk_size': 200, 'chunk_overlap': 20},
        {'chunk_size': 200, 'chunk_overlap': 50},
        {'chunk_size': 500, 'chunk_overlap': 50},
        {'chunk_size': 500, 'chunk_overlap': 100},
        {'chunk_size': 1000, 'chunk_overlap': 100},
        {'chunk_size': 1000, 'chunk_overlap': 200},
    ]

    # 2. 递归token分块器的参数配置
    configs['RecursiveTokenChunker'] = [
        {'chunk_size': 200, 'chunk_overlap': 20},
        {'chunk_size': 200, 'chunk_overlap': 50},
        {'chunk_size': 500, 'chunk_overlap': 50},
        {'chunk_size': 500, 'chunk_overlap': 100},
        {'chunk_size': 1000, 'chunk_overlap': 100},
        {'chunk_size': 1000, 'chunk_overlap': 200},
    ]

    # 3. 聚类语义分块器的参数配置 (暂时注释掉)
    # configs['ClusterSemanticChunker'] = [
    #     {'max_chunk_size': 200, 'min_chunk_size': 50},
    #     {'max_chunk_size': 400, 'min_chunk_size': 50},
    #     {'max_chunk_size': 400, 'min_chunk_size': 100},
    #     {'max_chunk_size': 600, 'min_chunk_size': 100},
    #     {'max_chunk_size': 800, 'min_chunk_size': 100},
    #     {'max_chunk_size': 1000, 'min_chunk_size': 200},
    # ]

    # 4. Kamradt改进版分块器的参数配置 (暂时注释掉)
    # configs['KamradtModifiedChunker'] = [
    #     {'avg_chunk_size': 200, 'min_chunk_size': 50},
    #     {'avg_chunk_size': 400, 'min_chunk_size': 50},
    #     {'avg_chunk_size': 400, 'min_chunk_size': 100},
    #     {'avg_chunk_size': 600, 'min_chunk_size': 100},
    #     {'avg_chunk_size': 800, 'min_chunk_size': 100},
    #     {'avg_chunk_size': 1000, 'min_chunk_size': 200},
    # ]

    return configs

def generate_db_name(chunker_type: str, params: Dict[str, Any]) -> str:
    """
    根据分块器类型和参数生成数据库名称

    参数:
        chunker_type: 分块器类型
        params: 参数字典

    返回:
        str: 格式化的数据库名称
    """
    # 将参数转换为字符串，按键排序确保一致性
    param_parts = []
    for key, value in sorted(params.items()):
        param_parts.append(f"{key}{value}")

    param_str = "_".join(param_parts)
    return f"{chunker_type}_{param_str}"

def check_db_exists(base_db_path: str, db_name: str) -> bool:
    """
    检查指定名称的数据库是否已存在

    参数:
        base_db_path: 数据库基础路径
        db_name: 数据库名称

    返回:
        bool: 数据库是否存在
    """
    db_path = Path(base_db_path) / db_name
    return db_path.exists() and any(db_path.iterdir())

def create_chunkers_with_configs() -> Dict[str, Any]:
    """
    创建所有分块器配置的实例

    返回:
        Dict[str, Any]: 分块器配置名称到分块器实例的映射
    """
    chunkers = {}
    configs = get_chunker_configs()

    # 创建embedding函数（用于语义分块器）- 暂时注释掉
    # try:
    #     embedding_function = get_openai_embedding_function()
    #     embedding_available = True
    # except Exception as e:
    #     print(f"警告：无法创建embedding函数: {e}")
    #     embedding_available = False

    # 为每种分块器创建不同参数配置的实例
    for chunker_type, param_configs in configs.items():
        for i, params in enumerate(param_configs):
            # 使用参数生成有意义的配置名称
            config_name = generate_db_name(chunker_type, params)

            try:
                if chunker_type == 'FixedTokenChunker':
                    chunkers[config_name] = {
                        'chunker': FixedTokenChunker(**params),
                        'params': params,
                        'type': chunker_type
                    }

                elif chunker_type == 'RecursiveTokenChunker':
                    chunkers[config_name] = {
                        'chunker': RecursiveTokenChunker(**params),
                        'params': params,
                        'type': chunker_type
                    }

                # elif chunker_type == 'ClusterSemanticChunker' and embedding_available:
                #     chunkers[config_name] = {
                #         'chunker': ClusterSemanticChunker(
                #             embedding_function=embedding_function,
                #             **params
                #         ),
                #         'params': params,
                #         'type': chunker_type
                #     }

                # elif chunker_type == 'KamradtModifiedChunker' and embedding_available:
                #     chunkers[config_name] = {
                #         'chunker': KamradtModifiedChunker(
                #             embedding_function=embedding_function,
                #             **params
                #         ),
                #         'params': params,
                #         'type': chunker_type
                #     }

            except Exception as e:
                print(f"警告：无法创建 {config_name}: {e}")

    return chunkers

def run_evaluation(questions_csv_path: str, base_chroma_db_path: str):
    """
    运行中文数据集评估

    参数:
        questions_csv_path (str): 中文问题数据集CSV文件路径
        base_chroma_db_path (str): ChromaDB数据库基础路径

    返回:
        Dict[str, Any]: 评估结果字典
    """
    print("🚀 开始中文数据集分块评估...")
    print("📋 本次评估将测试多种分块器在不同参数配置下的性能表现")
    print("🔄 系统将自动重用已有的向量数据库以提高效率")

    # 创建所有分块器配置
    chunkers = create_chunkers_with_configs()

    print(f"📊 总共将评估 {len(chunkers)} 种分块器配置")

    # 创建embedding函数
    try:
        embedding_function = get_openai_embedding_function()
    except Exception as e:
        print(f"错误：无法创建embedding函数: {e}")
        print("请确保设置了正确的OpenAI API密钥")
        return {}

    # 存储评估结果
    results = {}

    # 测试每个分块器配置
    for config_name, chunker_info in chunkers.items():
        chunker = chunker_info['chunker']
        params = chunker_info['params']
        chunker_type = chunker_info['type']

        # 生成有意义的数据库名称
        db_name = generate_db_name(chunker_type, params)
        specific_db_path = str(Path(base_chroma_db_path) / db_name)

        # 检查数据库是否已存在
        db_exists = check_db_exists(base_chroma_db_path, db_name)

        print(f"\n📊 正在评估 {config_name}")
        print(f"   分块器类型: {chunker_type}")
        print(f"   参数配置: {params}")
        print(f"   数据库名称: {db_name}")

        if db_exists:
            print(f"   ♻️  发现已有数据库，将重用现有向量数据")
        else:
            print(f"   🆕 创建新的向量数据库")

        try:
            # 创建评估器（每次都需要重新创建，因为数据库路径不同）
            evaluator = ChineseEvaluation(questions_csv_path, specific_db_path)

            # 运行评估
            result = evaluator.run(
                chunker=chunker,
                embedding_function=embedding_function,
                retrieve=5,
                db_to_save_chunks=specific_db_path
            )

            # 保存结果，包含参数信息和数据库信息
            results[config_name] = {
                **result,
                'chunker_type': chunker_type,
                'params': params,
                'db_name': db_name,
                'db_path': specific_db_path,
                'db_reused': db_exists
            }

            print(f"✅ {config_name} 评估完成")
            print(f"   精确率: {result.get('precision_mean', 'N/A'):.3f}")
            print(f"   召回率: {result.get('recall_mean', 'N/A'):.3f}")
            print(f"   IoU分数: {result.get('iou_mean', 'N/A'):.3f}")

        except Exception as e:
            print(f"❌ {config_name} 评估失败: {e}")
            results[config_name] = {
                "error": str(e),
                'chunker_type': chunker_type,
                'params': params,
                'db_name': db_name,
                'db_path': specific_db_path,
                'db_reused': db_exists
            }

    return results

def generate_report(results: Dict[str, Any]):
    """
    生成评估报告

    参数:
        results: 评估结果字典
    """
    print("\n" + "="*80)
    print("📋 中文数据集分块评估报告 - 多参数配置性能分析")
    print("="*80)

    # 创建详细结果表格
    report_data = []

    for config_name, result in results.items():
        chunker_type = result.get('chunker_type', 'Unknown')
        params = result.get('params', {})

        # 格式化参数字符串
        param_str = ", ".join([f"{k}={v}" for k, v in params.items()])

        if "error" in result:
            report_data.append({
                "配置名称": config_name,
                "分块器类型": chunker_type,
                "参数配置": param_str,
                "精确率": "错误",
                "召回率": "错误",
                "IoU分数": "错误",
                "状态": "失败"
            })
        else:
            report_data.append({
                "配置名称": config_name,
                "分块器类型": chunker_type,
                "参数配置": param_str,
                "精确率": f"{result.get('precision_mean', 0):.3f}",
                "召回率": f"{result.get('recall_mean', 0):.3f}",
                "IoU分数": f"{result.get('iou_mean', 0):.3f}",
                "状态": "成功"
            })

    # 创建DataFrame并显示
    df = pd.DataFrame(report_data)
    print(df.to_string(index=False))

    # 按分块器类型分组分析
    print("\n" + "="*80)
    print("📊 按分块器类型分组的性能分析")
    print("="*80)

    successful_results = {k: v for k, v in results.items() if "error" not in v}

    if successful_results:
        # 按分块器类型分组
        chunker_groups = {}
        for config_name, result in successful_results.items():
            chunker_type = result.get('chunker_type', 'Unknown')
            if chunker_type not in chunker_groups:
                chunker_groups[chunker_type] = []
            chunker_groups[chunker_type].append((config_name, result))

        # 为每种分块器类型找出最佳配置
        for chunker_type, configs in chunker_groups.items():
            print(f"\n🔍 {chunker_type} 性能分析:")

            # 找出该类型中的最佳配置
            best_iou_config = max(configs, key=lambda x: x[1].get('iou_mean', 0))
            best_precision_config = max(configs, key=lambda x: x[1].get('precision_mean', 0))
            best_recall_config = max(configs, key=lambda x: x[1].get('recall_mean', 0))

            print(f"   最佳IoU配置: {best_iou_config[0]}")
            print(f"     参数: {best_iou_config[1].get('params', {})}")
            print(f"     IoU分数: {best_iou_config[1].get('iou_mean', 0):.3f}")

            print(f"   最佳精确率配置: {best_precision_config[0]}")
            print(f"     参数: {best_precision_config[1].get('params', {})}")
            print(f"     精确率: {best_precision_config[1].get('precision_mean', 0):.3f}")

            print(f"   最佳召回率配置: {best_recall_config[0]}")
            print(f"     参数: {best_recall_config[1].get('params', {})}")
            print(f"     召回率: {best_recall_config[1].get('recall_mean', 0):.3f}")

        # 全局最佳配置
        print(f"\n🏆 全局最佳表现:")
        best_iou = max(successful_results.items(), key=lambda x: x[1].get('iou_mean', 0))
        best_precision = max(successful_results.items(), key=lambda x: x[1].get('precision_mean', 0))
        best_recall = max(successful_results.items(), key=lambda x: x[1].get('recall_mean', 0))

        print(f"   最佳IoU分数: {best_iou[0]} ({best_iou[1].get('iou_mean', 0):.3f})")
        print(f"     参数: {best_iou[1].get('params', {})}")
        print(f"   最佳精确率: {best_precision[0]} ({best_precision[1].get('precision_mean', 0):.3f})")
        print(f"     参数: {best_precision[1].get('params', {})}")
        print(f"   最佳召回率: {best_recall[0]} ({best_recall[1].get('recall_mean', 0):.3f})")
        print(f"     参数: {best_recall[1].get('params', {})}")

    # 保存结果到文件
    save_results(results)

def save_results(results: Dict[str, Any]):
    """
    保存评估结果到文件

    参数:
        results: 评估结果字典
    """
    # 创建输出目录
    output_dir = Path("chunking_evaluation/evaluation_framework/outputs")
    output_dir.mkdir(parents=True, exist_ok=True)

    # 保存详细结果
    output_file = output_dir / "chinese_evaluation_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"\n💾 详细结果已保存到: {output_file}")

    # 保存详细报告
    report_file = output_dir / "Chinese_evaluation_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("中文数据集分块评估报告 - 多参数配置性能分析\n")
        f.write("="*80 + "\n\n")

        # 按分块器类型分组写入报告
        successful_results = {k: v for k, v in results.items() if "error" not in v}
        failed_results = {k: v for k, v in results.items() if "error" in v}

        # 写入成功的结果
        if successful_results:
            f.write("📊 成功评估的配置:\n")
            f.write("-"*50 + "\n")

            # 按分块器类型分组
            chunker_groups = {}
            for config_name, result in successful_results.items():
                chunker_type = result.get('chunker_type', 'Unknown')
                if chunker_type not in chunker_groups:
                    chunker_groups[chunker_type] = []
                chunker_groups[chunker_type].append((config_name, result))

            # 为每种分块器类型写入详细信息
            for chunker_type, configs in chunker_groups.items():
                f.write(f"\n🔍 {chunker_type}:\n")

                for config_name, result in configs:
                    params = result.get('params', {})
                    param_str = ", ".join([f"{k}={v}" for k, v in params.items()])

                    f.write(f"  配置: {config_name}\n")
                    f.write(f"  参数: {param_str}\n")
                    f.write(f"  精确率: {result.get('precision_mean', 0):.3f}\n")
                    f.write(f"  召回率: {result.get('recall_mean', 0):.3f}\n")
                    f.write(f"  IoU分数: {result.get('iou_mean', 0):.3f}\n")
                    f.write("  " + "-"*40 + "\n")

                # 找出该类型中的最佳配置
                best_iou_config = max(configs, key=lambda x: x[1].get('iou_mean', 0))
                f.write(f"  🏆 最佳IoU配置: {best_iou_config[0]} (IoU: {best_iou_config[1].get('iou_mean', 0):.3f})\n")
                f.write(f"     参数: {best_iou_config[1].get('params', {})}\n\n")

        # 写入失败的结果
        if failed_results:
            f.write("\n❌ 失败的配置:\n")
            f.write("-"*50 + "\n")
            for config_name, result in failed_results.items():
                chunker_type = result.get('chunker_type', 'Unknown')
                params = result.get('params', {})
                param_str = ", ".join([f"{k}={v}" for k, v in params.items()])

                f.write(f"配置: {config_name}\n")
                f.write(f"类型: {chunker_type}\n")
                f.write(f"参数: {param_str}\n")
                f.write(f"错误: {result['error']}\n")
                f.write("-"*30 + "\n")

        # 写入全局最佳配置
        if successful_results:
            f.write("\n🏆 全局最佳表现:\n")
            f.write("-"*50 + "\n")

            best_iou = max(successful_results.items(), key=lambda x: x[1].get('iou_mean', 0))
            best_precision = max(successful_results.items(), key=lambda x: x[1].get('precision_mean', 0))
            best_recall = max(successful_results.items(), key=lambda x: x[1].get('recall_mean', 0))

            f.write(f"最佳IoU分数: {best_iou[0]} ({best_iou[1].get('iou_mean', 0):.3f})\n")
            f.write(f"  参数: {best_iou[1].get('params', {})}\n")
            f.write(f"最佳精确率: {best_precision[0]} ({best_precision[1].get('precision_mean', 0):.3f})\n")
            f.write(f"  参数: {best_precision[1].get('params', {})}\n")
            f.write(f"最佳召回率: {best_recall[0]} ({best_recall[1].get('recall_mean', 0):.3f})\n")
            f.write(f"  参数: {best_recall[1].get('params', {})}\n")

    print(f"📄 评估报告已保存到: {report_file}")

def analyze_parameter_trends(results: Dict[str, Any]):
    """
    分析参数设置对性能的影响趋势

    参数:
        results: 评估结果字典
    """
    print("\n" + "="*80)
    print("📈 参数影响趋势分析")
    print("="*80)

    successful_results = {k: v for k, v in results.items() if "error" not in v}

    if not successful_results:
        print("❌ 没有成功的评估结果可供分析")
        return

    # 按分块器类型分组分析
    chunker_groups = {}
    for config_name, result in successful_results.items():
        chunker_type = result.get('chunker_type', 'Unknown')
        if chunker_type not in chunker_groups:
            chunker_groups[chunker_type] = []
        chunker_groups[chunker_type].append((config_name, result))

    for chunker_type, configs in chunker_groups.items():
        print(f"\n🔍 {chunker_type} 参数趋势分析:")

        # 提取参数和性能数据
        param_data = []
        for config_name, result in configs:
            params = result.get('params', {})
            param_data.append({
                'config_name': config_name,
                'iou_mean': result.get('iou_mean', 0),
                'precision_mean': result.get('precision_mean', 0),
                'recall_mean': result.get('recall_mean', 0),
                **params
            })

        # 分析不同参数的影响
        if chunker_type in ['FixedTokenChunker', 'RecursiveTokenChunker']:
            # 分析chunk_size的影响
            chunk_sizes = sorted(set([d['chunk_size'] for d in param_data]))
            print(f"   📊 chunk_size 影响分析 (测试值: {chunk_sizes}):")

            for chunk_size in chunk_sizes:
                size_configs = [d for d in param_data if d['chunk_size'] == chunk_size]
                avg_iou = sum([d['iou_mean'] for d in size_configs]) / len(size_configs)
                avg_precision = sum([d['precision_mean'] for d in size_configs]) / len(size_configs)
                avg_recall = sum([d['recall_mean'] for d in size_configs]) / len(size_configs)

                print(f"     chunk_size={chunk_size}: IoU={avg_iou:.3f}, 精确率={avg_precision:.3f}, 召回率={avg_recall:.3f}")

            # 分析chunk_overlap的影响
            chunk_overlaps = sorted(set([d['chunk_overlap'] for d in param_data]))
            print(f"   📊 chunk_overlap 影响分析 (测试值: {chunk_overlaps}):")

            for chunk_overlap in chunk_overlaps:
                overlap_configs = [d for d in param_data if d['chunk_overlap'] == chunk_overlap]
                avg_iou = sum([d['iou_mean'] for d in overlap_configs]) / len(overlap_configs)
                avg_precision = sum([d['precision_mean'] for d in overlap_configs]) / len(overlap_configs)
                avg_recall = sum([d['recall_mean'] for d in overlap_configs]) / len(overlap_configs)

                print(f"     chunk_overlap={chunk_overlap}: IoU={avg_iou:.3f}, 精确率={avg_precision:.3f}, 召回率={avg_recall:.3f}")

        elif chunker_type == 'ClusterSemanticChunker':
            # 分析max_chunk_size的影响
            max_chunk_sizes = sorted(set([d['max_chunk_size'] for d in param_data]))
            print(f"   📊 max_chunk_size 影响分析 (测试值: {max_chunk_sizes}):")

            for max_chunk_size in max_chunk_sizes:
                size_configs = [d for d in param_data if d['max_chunk_size'] == max_chunk_size]
                avg_iou = sum([d['iou_mean'] for d in size_configs]) / len(size_configs)
                avg_precision = sum([d['precision_mean'] for d in size_configs]) / len(size_configs)
                avg_recall = sum([d['recall_mean'] for d in size_configs]) / len(size_configs)

                print(f"     max_chunk_size={max_chunk_size}: IoU={avg_iou:.3f}, 精确率={avg_precision:.3f}, 召回率={avg_recall:.3f}")

            # 分析min_chunk_size的影响
            min_chunk_sizes = sorted(set([d['min_chunk_size'] for d in param_data]))
            print(f"   📊 min_chunk_size 影响分析 (测试值: {min_chunk_sizes}):")

            for min_chunk_size in min_chunk_sizes:
                size_configs = [d for d in param_data if d['min_chunk_size'] == min_chunk_size]
                avg_iou = sum([d['iou_mean'] for d in size_configs]) / len(size_configs)
                avg_precision = sum([d['precision_mean'] for d in size_configs]) / len(size_configs)
                avg_recall = sum([d['recall_mean'] for d in size_configs]) / len(size_configs)

                print(f"     min_chunk_size={min_chunk_size}: IoU={avg_iou:.3f}, 精确率={avg_precision:.3f}, 召回率={avg_recall:.3f}")

        elif chunker_type == 'KamradtModifiedChunker':
            # 分析avg_chunk_size的影响
            avg_chunk_sizes = sorted(set([d['avg_chunk_size'] for d in param_data]))
            print(f"   📊 avg_chunk_size 影响分析 (测试值: {avg_chunk_sizes}):")

            for avg_chunk_size in avg_chunk_sizes:
                size_configs = [d for d in param_data if d['avg_chunk_size'] == avg_chunk_size]
                avg_iou = sum([d['iou_mean'] for d in size_configs]) / len(size_configs)
                avg_precision = sum([d['precision_mean'] for d in size_configs]) / len(size_configs)
                avg_recall = sum([d['recall_mean'] for d in size_configs]) / len(size_configs)

                print(f"     avg_chunk_size={avg_chunk_size}: IoU={avg_iou:.3f}, 精确率={avg_precision:.3f}, 召回率={avg_recall:.3f}")

            # 分析min_chunk_size的影响
            min_chunk_sizes = sorted(set([d['min_chunk_size'] for d in param_data]))
            print(f"   📊 min_chunk_size 影响分析 (测试值: {min_chunk_sizes}):")

            for min_chunk_size in min_chunk_sizes:
                size_configs = [d for d in param_data if d['min_chunk_size'] == min_chunk_size]
                avg_iou = sum([d['iou_mean'] for d in size_configs]) / len(size_configs)
                avg_precision = sum([d['precision_mean'] for d in size_configs]) / len(size_configs)
                avg_recall = sum([d['recall_mean'] for d in size_configs]) / len(size_configs)

                print(f"     min_chunk_size={min_chunk_size}: IoU={avg_iou:.3f}, 精确率={avg_precision:.3f}, 召回率={avg_recall:.3f}")

        # 找出该分块器类型的最优参数组合
        best_config = max(configs, key=lambda x: x[1].get('iou_mean', 0))
        print(f"   🏆 推荐参数配置: {best_config[1].get('params', {})}")
        print(f"     性能: IoU={best_config[1].get('iou_mean', 0):.3f}, 精确率={best_config[1].get('precision_mean', 0):.3f}, 召回率={best_config[1].get('recall_mean', 0):.3f}")

def main():
    """
    主函数
    """
    print("🎯 中文数据集分块评估工具")
    print("="*50)

    # 数据集路径配置
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    base_chroma_db_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db"

    # 检查数据集是否存在
    if not os.path.exists(questions_csv_path):
        print(f"❌ 错误：找不到中文数据集文件: {questions_csv_path}")
        print("请确保已经创建了中文数据集")
        return

    print(f"✅ 找到中文数据集: {questions_csv_path}")
    print(f"📁 向量数据库基础路径: {base_chroma_db_path}")

    # 运行评估
    results = run_evaluation(questions_csv_path, base_chroma_db_path)

    if results:
        # 生成评估报告
        generate_report(results)

        # 分析参数趋势
        analyze_parameter_trends(results)

        print("\n🎉 评估完成！")
        print("\n📁 详细结果已保存到 evaluation_framework/outputs/ 目录")
        print("   - chinese_evaluation_results.json: 完整的评估数据")
        print("   - Chinese_evaluation_report.txt: 详细的分析报告")
    else:
        print("\n❌ 评估过程中出现错误，请检查配置和网络连接。")

if __name__ == "__main__":
    main()