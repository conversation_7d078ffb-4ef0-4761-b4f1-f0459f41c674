#!/usr/bin/env python3
"""
中文数据集分块评估脚本

这个脚本专门用于评估不同分块策略在中文数据集上的表现。
它使用您已创建的中文问题-答案对来测试各种分块算法的效果。

主要功能：
1. 测试多种分块策略（固定token、递归token、语义聚类等）
2. 使用中文数据集进行评估
3. 生成详细的评估报告和比较结果

使用方法：
python run_chinese_evaluation.py
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.evaluation_framework.base_evaluation import BaseEvaluation
from chunking_evaluation.chunking import (
    FixedTokenChunker,
    RecursiveTokenChunker,
    ClusterSemanticChunker,
    KamradtModifiedChunker
)
from chunking_evaluation.utils import get_openai_embedding_function

class ChineseEvaluation(BaseEvaluation):
    """
    中文数据集评估类

    专门用于评估分块策略在中文数据集上的表现。
    继承自BaseEvaluation，使用中文问题-答案对进行评估。
    """

    def __init__(self, questions_csv_path: str, chroma_db_path: str = None):
        """
        初始化中文评估类

        参数:
            questions_csv_path (str): 中文问题数据集CSV文件路径
            chroma_db_path (str, 可选): ChromaDB数据库路径
        """
        # 构建语料库路径映射
        corpora_id_paths = self._build_corpora_paths()

        # 调用父类构造函数
        super().__init__(questions_csv_path, chroma_db_path=chroma_db_path, corpora_id_paths=corpora_id_paths)

    def _build_corpora_paths(self) -> Dict[str, str]:
        """
        构建语料库ID到文件路径的映射

        返回:
            Dict[str, str]: 语料库ID到文件路径的映射字典
        """
        corpora_paths = {}

        # 获取当前脚本所在目录
        current_dir = Path(__file__).parent

        # datasets目录路径
        datasets_dir = current_dir / "evaluation_framework" / "general_evaluation_data" / "datasets"

        # 检查datasets目录是否存在
        if not datasets_dir.exists():
            print(f"警告：datasets目录不存在: {datasets_dir}")
            return corpora_paths

        # 遍历datasets目录下的所有子目录
        for subdir in datasets_dir.iterdir():
            if subdir.is_dir():
                # 遍历该子目录下的所有.txt文件
                for file in subdir.iterdir():
                    if file.suffix == '.txt':
                        # 使用绝对路径作为corpus_id，以匹配CSV文件中的路径
                        absolute_path = str(file.absolute())
                        corpora_paths[absolute_path] = str(file)

        return corpora_paths

def create_chunkers() -> Dict[str, Any]:
    """
    创建要测试的分块器列表

    返回:
        Dict[str, Any]: 分块器名称到分块器实例的映射
    """
    chunkers = {}

    # 1. 固定token分块器
    chunkers['FixedTokenChunker'] = FixedTokenChunker(
        chunk_size=200,
        chunk_overlap=50
    )

    # 2. 递归token分块器
    chunkers['RecursiveTokenChunker'] = RecursiveTokenChunker(
        chunk_size=1000,
        chunk_overlap=100
    )

    # # 3. 聚类语义分块器
    # try:
    #     embedding_function = get_openai_embedding_function()
    #     chunkers['ClusterSemanticChunker'] = ClusterSemanticChunker(
    #         embedding_function=embedding_function,
    #         max_chunk_size=1000,
    #         min_chunk_size=100
    #     )
    # except Exception as e:
    #     print(f"警告：无法创建ClusterSemanticChunker: {e}")

    # # 4. Kamradt改进版分块器
    # try:
    #     embedding_function = get_openai_embedding_function()
    #     chunkers['KamradtModifiedChunker'] = KamradtModifiedChunker(
    #         avg_chunk_size=1000,
    #         min_chunk_size=100,
    #         embedding_function=embedding_function
    #     )
    # except Exception as e:
    #     print(f"警告：无法创建KamradtModifiedChunker: {e}")

    return chunkers

def run_evaluation(questions_csv_path: str, chroma_db_path: str):
    """
    运行中文数据集评估

    参数:
        questions_csv_path (str): 中文问题数据集CSV文件路径
        chroma_db_path (str): ChromaDB数据库路径

    返回:
        Dict[str, Any]: 评估结果字典
    """
    print("🚀 开始中文数据集分块评估...")

    # 创建评估器
    evaluator = ChineseEvaluation(questions_csv_path, chroma_db_path)

    # 创建分块器
    chunkers = create_chunkers()

    # 创建embedding函数
    try:
        embedding_function = get_openai_embedding_function()
    except Exception as e:
        print(f"错误：无法创建embedding函数: {e}")
        print("请确保设置了正确的OpenAI API密钥")
        return {}

    # 存储评估结果
    results = {}

    # 测试每个分块器
    for chunker_name, chunker in chunkers.items():
        print(f"\n📊 正在评估 {chunker_name}...")

        try:
            # 运行评估
            result = evaluator.run(
                chunker=chunker,
                embedding_function=embedding_function,
                retrieve=5,
                db_to_save_chunks=chroma_db_path
            )

            results[chunker_name] = result
            print(f"✅ {chunker_name} 评估完成")
            print(f"   精确率: {result.get('precision_mean', 'N/A'):.3f}")
            print(f"   召回率: {result.get('recall_mean', 'N/A'):.3f}")
            print(f"   IoU分数: {result.get('iou_mean', 'N/A'):.3f}")

        except Exception as e:
            print(f"❌ {chunker_name} 评估失败: {e}")
            results[chunker_name] = {"error": str(e)}

    return results

def generate_report(results: Dict[str, Any]):
    """
    生成评估报告

    参数:
        results: 评估结果字典
    """
    print("\n" + "="*60)
    print("📋 中文数据集分块评估报告")
    print("="*60)

    # 创建结果表格
    report_data = []

    for chunker_name, result in results.items():
        if "error" in result:
            report_data.append({
                "分块器": chunker_name,
                "精确率": "错误",
                "召回率": "错误",
                "IoU分数": "错误",
                "状态": "失败"
            })
        else:
            report_data.append({
                "分块器": chunker_name,
                "精确率": f"{result.get('precision_mean', 0):.3f}",
                "召回率": f"{result.get('recall_mean', 0):.3f}",
                "IoU分数": f"{result.get('iou_mean', 0):.3f}",
                "状态": "成功"
            })

    # 创建DataFrame并显示
    df = pd.DataFrame(report_data)
    print(df.to_string(index=False))

    # 找出最佳表现的分块器
    successful_results = {k: v for k, v in results.items() if "error" not in v}

    if successful_results:
        best_iou = max(successful_results.items(),
                      key=lambda x: x[1].get('iou_mean', 0))
        best_precision = max(successful_results.items(),
                           key=lambda x: x[1].get('precision_mean', 0))
        best_recall = max(successful_results.items(),
                         key=lambda x: x[1].get('recall_mean', 0))

        print(f"\n🏆 最佳表现:")
        print(f"   最佳IoU分数: {best_iou[0]} ({best_iou[1].get('iou_mean', 0):.3f})")
        print(f"   最佳精确率: {best_precision[0]} ({best_precision[1].get('precision_mean', 0):.3f})")
        print(f"   最佳召回率: {best_recall[0]} ({best_recall[1].get('recall_mean', 0):.3f})")

    # 保存结果到文件
    save_results(results)

def save_results(results: Dict[str, Any]):
    """
    保存评估结果到文件

    参数:
        results: 评估结果字典
    """
    # 保存详细结果
    output_file = "chinese_evaluation_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"\n💾 详细结果已保存到: {output_file}")

    # 保存简化报告
    report_file = "chinese_evaluation_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("中文数据集分块评估报告\n")
        f.write("="*50 + "\n\n")

        for chunker_name, result in results.items():
            f.write(f"分块器: {chunker_name}\n")
            if "error" in result:
                f.write(f"状态: 失败 - {result['error']}\n")
            else:
                f.write(f"精确率: {result.get('precision_mean', 0):.3f}\n")
                f.write(f"召回率: {result.get('recall_mean', 0):.3f}\n")
                f.write(f"IoU分数: {result.get('iou_mean', 0):.3f}\n")
            f.write("-"*30 + "\n")

    print(f"📄 评估报告已保存到: {report_file}")

def main():
    """
    主函数
    """
    print("🎯 中文数据集分块评估工具")
    print("="*50)

    # 数据集路径配置
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions.csv"
    chroma_db_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/chinese_questions_db"

    # 检查数据集是否存在
    if not os.path.exists(questions_csv_path):
        print(f"❌ 错误：找不到中文数据集文件: {questions_csv_path}")
        print("请确保已经创建了中文数据集")
        return

    print(f"✅ 找到中文数据集: {questions_csv_path}")

    # 运行评估
    results = run_evaluation(questions_csv_path, chroma_db_path)

    if results:
        # 生成评估报告
        generate_report(results)
        print("\n🎉 评估完成！")
        print("请查看生成的报告文件了解详细结果。")
    else:
        print("\n❌ 评估过程中出现错误，请检查配置和网络连接。")

if __name__ == "__main__":
    main()