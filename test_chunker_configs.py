#!/usr/bin/env python3
"""
测试分块器配置功能的简单脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from chunking_evaluation.run_chinese_evaluation import get_chunker_configs, create_chunkers_with_configs

def test_chunker_configs():
    """测试分块器配置功能"""
    print("🧪 测试分块器配置功能")
    print("="*50)
    
    # 测试配置获取
    print("\n1. 测试配置获取:")
    configs = get_chunker_configs()
    
    for chunker_type, param_list in configs.items():
        print(f"\n📋 {chunker_type}:")
        for i, params in enumerate(param_list):
            print(f"   配置 {i+1}: {params}")
    
    print(f"\n✅ 总共定义了 {sum(len(params) for params in configs.values())} 种参数配置")
    
    # 测试分块器创建（不需要embedding函数的部分）
    print("\n2. 测试分块器创建:")
    try:
        chunkers = create_chunkers_with_configs()
        
        # 统计创建成功的分块器
        successful_chunkers = {}
        for config_name, chunker_info in chunkers.items():
            chunker_type = chunker_info['type']
            if chunker_type not in successful_chunkers:
                successful_chunkers[chunker_type] = 0
            successful_chunkers[chunker_type] += 1
        
        print(f"✅ 成功创建了 {len(chunkers)} 个分块器实例")
        
        for chunker_type, count in successful_chunkers.items():
            print(f"   {chunker_type}: {count} 个配置")
        
        # 显示几个示例
        print("\n📋 示例配置:")
        for i, (config_name, chunker_info) in enumerate(list(chunkers.items())[:3]):
            print(f"   {config_name}:")
            print(f"     类型: {chunker_info['type']}")
            print(f"     参数: {chunker_info['params']}")
            
    except Exception as e:
        print(f"❌ 创建分块器时出错: {e}")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    test_chunker_configs()
